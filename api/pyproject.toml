[tool.poetry]
name = "luyabaike-api"
version = "0.1.0"
description = "路亚百科API - 为路亚爱好者提供装备和基地信息查询"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.13.5"
fastapi = "0.116.1"
sqlmodel = "0.0.24"
pydantic = {extras = ["email"], version = "2.11.7"}
pydantic-settings = "2.10.1"
uvicorn = {extras = ["standard"], version = "0.35.0"}
pymysql = "^1.1.1"
cryptography = "^44.0.0"
python-multipart = "^0.0.20"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-dotenv = "^1.0.1"
alembic = "^1.14.0"
python-json-logger = "^2.0.7"
requests = "^2.32.4"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.4"
pytest-asyncio = "^0.25.0"
httpx = "^0.28.1"
black = "^24.10.0"
isort = "^5.13.2"
flake8 = "^7.1.1"
mypy = "^1.14.0"
pre-commit = "^4.0.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88

[tool.mypy]
python_version = "3.13"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
asyncio_mode = "auto"
