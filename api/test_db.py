#!/usr/bin/env python3
"""
数据库连接测试脚本
"""

import os
from sqlmodel import Session, text
from app.db.session import engine
from app.core.config import settings

def test_database_connection():
    """测试数据库连接"""
    print("🔍 Testing database connection...")
    print(f"Database URL: {settings.database_url}")
    
    try:
        with Session(engine) as session:
            # 测试基本连接
            result = session.exec(text("SELECT 1 as test")).first()
            if result:
                print("✅ Database connection successful")
                
                # 测试数据库和表是否存在
                try:
                    # 检查数据库
                    db_result = session.exec(text(f"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{settings.database_name}'")).first()
                    if db_result:
                        print(f"✅ Database '{settings.database_name}' exists")
                        
                        # 检查主要表是否存在
                        tables_to_check = ['user', 'fish', 'rod', 'reel', 'fishing_location']
                        for table in tables_to_check:
                            table_result = session.exec(text(f"SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '{settings.database_name}' AND TABLE_NAME = '{table}'")).first()
                            if table_result:
                                print(f"✅ Table '{table}' exists")
                            else:
                                print(f"❌ Table '{table}' does not exist")
                    else:
                        print(f"❌ Database '{settings.database_name}' does not exist")
                        
                except Exception as e:
                    print(f"❌ Error checking database structure: {e}")
                    
                return True
            else:
                print("❌ Database connection test failed")
                return False
                
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_user_table():
    """测试用户表操作"""
    print("\n🔍 Testing user table operations...")
    
    try:
        with Session(engine) as session:
            # 测试查询用户表
            result = session.exec(text("SELECT COUNT(*) as count FROM user")).first()
            print(f"✅ User table query successful, count: {result}")
            return True
    except Exception as e:
        print(f"❌ User table operation failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting database tests...\n")

    # 检查配置是否正确加载
    try:
        print(f"Database configuration:")
        print(f"  Host: {settings.database_host}")
        print(f"  Port: {settings.database_port}")
        print(f"  User: {settings.database_user}")
        print(f"  Database: {settings.database_name}")
        print(f"  URL: {settings.database_url}")
        print()
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        exit(1)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("User Table Operations", test_user_table),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} - Unexpected error: {e}")
    
    print(f"\n📊 Database Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All database tests passed!")
    else:
        print("⚠️  Some database tests failed. Check the output above for details.")
