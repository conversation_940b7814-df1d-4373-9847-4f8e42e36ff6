version: '3.8'

services:
  # 路亚百科API服务
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_HOST=mysql
      - DATABASE_PORT=3306
      - DATABASE_USER=root
      - DATABASE_PASSWORD=cdc123321
      - DATABASE_NAME=luyabaike
      - DEBUG=true
    volumes:
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - luyabaike-network

  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=cdc123321
      - MYSQL_DATABASE=luyabaike
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docs/luyabaike.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    networks:
      - luyabaike-network

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - luyabaike-network

volumes:
  mysql_data:
  redis_data:

networks:
  luyabaike-network:
    driver: bridge
