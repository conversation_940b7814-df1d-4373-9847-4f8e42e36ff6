"""
自定义异常和异常处理器
"""

import logging
from typing import Union
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

logger = logging.getLogger(__name__)


class LuyaBaikeException(Exception):
    """路亚百科自定义异常基类"""
    
    def __init__(self, message: str, code: int = 500):
        self.message = message
        self.code = code
        super().__init__(self.message)


class BusinessException(LuyaBaikeException):
    """业务逻辑异常"""
    
    def __init__(self, message: str, code: int = 400):
        super().__init__(message, code)


class DatabaseException(LuyaBaikeException):
    """数据库异常"""
    
    def __init__(self, message: str, code: int = 500):
        super().__init__(message, code)


class ValidationException(LuyaBaikeException):
    """数据验证异常"""
    
    def __init__(self, message: str, code: int = 422):
        super().__init__(message, code)


async def luya_baike_exception_handler(request: Request, exc: LuyaBaikeException):
    """路亚百科自定义异常处理器"""
    logger.error(f"LuyaBaikeException: {exc.message}", exc_info=True)
    return JSONResponse(
        status_code=exc.code,
        content={
            "code": exc.code,
            "message": exc.message,
            "data": None
        }
    )


async def http_exception_handler(request: Request, exc: Union[HTTPException, StarletteHTTPException]):
    """HTTP异常处理器"""
    logger.warning(f"HTTP Exception: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.detail,
            "data": None
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.warning(f"Validation Error: {exc.errors()}")
    
    # 格式化验证错误信息
    errors = []
    for error in exc.errors():
        field = ".".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        errors.append(f"{field}: {message}")
    
    return JSONResponse(
        status_code=422,
        content={
            "code": 422,
            "message": "请求参数验证失败",
            "data": {
                "errors": errors,
                "detail": exc.errors()
            }
        }
    )


async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
    """SQLAlchemy异常处理器"""
    logger.error(f"Database Error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "数据库操作失败",
            "data": None
        }
    )


async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"Unexpected Error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "服务器内部错误",
            "data": None
        }
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """设置异常处理器"""
    
    # 自定义异常处理器
    app.add_exception_handler(LuyaBaikeException, luya_baike_exception_handler)
    
    # HTTP异常处理器
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # 验证异常处理器
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # 数据库异常处理器
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    
    # 通用异常处理器
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("异常处理器已设置完成")
