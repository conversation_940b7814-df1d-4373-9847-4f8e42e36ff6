"""
Pydantic模型模块
用于API请求和响应的数据验证
"""

from .common import (
    ResponseModel,
    PaginationModel,
    PaginatedResponseModel,
    SearchParams,
    ErrorModel
)
from .fishing_location import (
    FishingLocationSearchParams,
    FishingLocationResponse,
    FishingLocationDetailResponse,
    FishingLocationListItem,
    FishingLocationCreate,
    FishingLocationUpdate
)
from .rod import (
    RodSearchParams,
    RodResponse,
    RodItemResponse,
    RodDetailResponse,
    RodListItem,
    RodCreate,
    RodUpdate
)
from .reel import (
    ReelSearchParams,
    ReelResponse,
    ReelItemResponse,
    ReelDetailResponse,
    ReelListItem,
    ReelCreate,
    ReelUpdate
)
from .auth import (
    Token,
    TokenData,
    UserLogin,
    UserRegister,
    UserProfile,
    UserUpdate,
    PasswordChange,
    PasswordReset,
    PasswordResetConfirm,
    EmailVerification,
    LoginResponse,
    RegisterResponse
)
from .fish import (
    FishSearchParams,
    FishCreate,
    FishUpdate,
    FishResponse,
    FishListItem,
    FishDetailResponse
)

__all__ = [
    # Common schemas
    "ResponseModel",
    "PaginationModel",
    "PaginatedResponseModel",
    "SearchParams",
    "ErrorModel",
    # FishingLocation schemas
    "FishingLocationSearchParams",
    "FishingLocationResponse",
    "FishingLocationDetailResponse",
    "FishingLocationListItem",
    "FishingLocationCreate",
    "FishingLocationUpdate",
    # Rod schemas
    "RodSearchParams",
    "RodResponse",
    "RodItemResponse",
    "RodDetailResponse",
    "RodListItem",
    "RodCreate",
    "RodUpdate",
    # Reel schemas
    "ReelSearchParams",
    "ReelResponse",
    "ReelItemResponse",
    "ReelDetailResponse",
    "ReelListItem",
    "ReelCreate",
    "ReelUpdate",
    # Auth schemas
    "Token",
    "TokenData",
    "UserLogin",
    "UserRegister",
    "UserProfile",
    "UserUpdate",
    "PasswordChange",
    "PasswordReset",
    "PasswordResetConfirm",
    "EmailVerification",
    "LoginResponse",
    "RegisterResponse",
    # Fish schemas
    "FishSearchParams",
    "FishCreate",
    "FishUpdate",
    "FishResponse",
    "FishListItem",
    "FishDetailResponse",
]