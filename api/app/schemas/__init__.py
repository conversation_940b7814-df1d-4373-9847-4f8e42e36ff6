"""
Pydantic模型模块
用于API请求和响应的数据验证
"""

from .common import (
    ResponseModel,
    PaginationModel,
    PaginatedResponseModel,
    SearchParams,
    ErrorModel
)
from .fishing_location import (
    FishingLocationSearchParams,
    FishingLocationResponse,
    FishingLocationDetailResponse,
    FishingLocationListItem
)
from .rod import (
    RodSearchParams,
    RodResponse,
    RodItemResponse,
    RodDetailResponse,
    RodListItem
)
from .reel import (
    ReelSearchParams,
    ReelResponse,
    ReelItemResponse,
    ReelDetailResponse,
    ReelListItem
)

__all__ = [
    # Common schemas
    "ResponseModel",
    "PaginationModel",
    "PaginatedResponseModel",
    "SearchParams",
    "ErrorModel",
    # FishingLocation schemas
    "FishingLocationSearchParams",
    "FishingLocationResponse",
    "FishingLocationDetailResponse",
    "FishingLocationListItem",
    # Rod schemas
    "RodSearchParams",
    "RodResponse",
    "RodItemResponse",
    "RodDetailResponse",
    "RodListItem",
    # Reel schemas
    "ReelSearchParams",
    "ReelResponse",
    "ReelItemResponse",
    "ReelDetailResponse",
    "ReelListItem",
]