"""
渔轮相关的Pydantic模型
用于API请求和响应的数据验证
"""

from typing import Optional, List, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field
from .common import SearchParams


class ReelSearchParams(SearchParams):
    """渔轮搜索参数"""
    company_id: Optional[int] = Field(default=None, description="品牌ID")
    series_id: Optional[int] = Field(default=None, description="系列ID")
    type: Optional[str] = Field(default=None, description="渔轮类型")
    gear_ratio_min: Optional[Decimal] = Field(default=None, description="最小速比")
    gear_ratio_max: Optional[Decimal] = Field(default=None, description="最大速比")
    weight_min: Optional[int] = Field(default=None, description="最小重量")
    weight_max: Optional[int] = Field(default=None, description="最大重量")


class ReelResponse(BaseModel):
    """渔轮响应模型"""
    id: int = Field(description="渔轮ID")
    uuid: str = Field(description="UUID")
    name: str = Field(description="渔轮名称")
    type: str = Field(description="渔轮类型")
    company_id: Optional[int] = Field(default=None, description="品牌ID")
    company_name: Optional[str] = Field(default=None, description="品牌名称")
    series_id: Optional[int] = Field(default=None, description="系列ID")
    series_name: Optional[str] = Field(default=None, description="系列名称")
    cover: Optional[str] = Field(default=None, description="封面图片")
    desc: Optional[str] = Field(default=None, description="描述")


class ReelItemResponse(BaseModel):
    """渔轮型号响应模型"""
    id: int = Field(description="型号ID")
    uuid: str = Field(description="UUID")
    reel_id: int = Field(description="渔轮ID")
    name: str = Field(description="型号名称")
    gear_ratio: Optional[Decimal] = Field(default=None, description="速比")
    cover: Optional[str] = Field(default=None, description="型号图片")
    desc: Optional[str] = Field(default=None, description="型号说明")
    short_desc: Optional[str] = Field(default=None, description="一句话描述")
    best_drag: Optional[str] = Field(default=None, description="实用卸力kg")
    max_drag: Optional[Decimal] = Field(default=None, description="最大卸力kg")
    weight: Optional[int] = Field(default=None, description="重量g")
    spool: Optional[str] = Field(default=None, description="线杯参数")
    bearings: Optional[str] = Field(default=None, description="轴承数")
    nylon_capacity: Optional[Dict[str, Any]] = Field(default=None, description="尼龙线容量")
    pe_capacity: Optional[Dict[str, Any]] = Field(default=None, description="PE线容量")
    fluorocarbon_capacity: Optional[Dict[str, Any]] = Field(default=None, description="碳氟线容量")
    line_per_crank: Optional[int] = Field(default=None, description="手把转一圈最大收线长")
    handle_length: Optional[int] = Field(default=None, description="摇臂长度")
    handle_position: Optional[str] = Field(default=None, description="摇臂位置LEFT/RIGHT")
    release_year: Optional[int] = Field(default=None, description="发售日期")


class ReelDetailResponse(ReelResponse):
    """渔轮详情响应模型"""
    items: List[ReelItemResponse] = Field(default_factory=list, description="型号列表")


class ReelListItem(BaseModel):
    """渔轮列表项模型"""
    id: int = Field(description="渔轮ID")
    uuid: str = Field(description="UUID")
    name: str = Field(description="渔轮名称")
    type: str = Field(description="渔轮类型")
    company_name: Optional[str] = Field(default=None, description="品牌名称")
    series_name: Optional[str] = Field(default=None, description="系列名称")
    cover: Optional[str] = Field(default=None, description="封面图片")
    item_count: int = Field(default=0, description="型号数量")
    price_range: Optional[str] = Field(default=None, description="价格区间")
