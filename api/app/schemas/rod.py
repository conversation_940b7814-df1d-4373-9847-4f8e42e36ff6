"""
路亚竿相关的Pydantic模型
用于API请求和响应的数据验证
"""

from typing import Optional, List, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field
from .common import SearchParams


class RodSearchParams(SearchParams):
    """路亚竿搜索参数"""
    company_id: Optional[int] = Field(default=None, description="品牌ID")
    series_id: Optional[int] = Field(default=None, description="系列ID")
    min_lure_wt: Optional[Decimal] = Field(default=None, description="最小饵重")
    max_lure_wt: Optional[Decimal] = Field(default=None, description="最大饵重")
    action: Optional[str] = Field(default=None, description="调性")
    power: Optional[str] = Field(default=None, description="硬度")
    reel_seat_type: Optional[str] = Field(default=None, description="枪柄/直柄")


class RodResponse(BaseModel):
    """路亚竿响应模型"""
    id: int = Field(description="路亚竿ID")
    uuid: str = Field(description="UUID")
    name: str = Field(description="路亚竿名称")
    company_id: Optional[int] = Field(default=None, description="品牌ID")
    company_name: Optional[str] = Field(default=None, description="品牌名称")
    series_id: Optional[int] = Field(default=None, description="系列ID")
    series_name: Optional[str] = Field(default=None, description="系列名称")
    cover: Optional[str] = Field(default=None, description="封面图片")
    desc: Optional[str] = Field(default=None, description="描述")


class RodItemResponse(BaseModel):
    """路亚竿型号响应模型"""
    id: int = Field(description="型号ID")
    uuid: str = Field(description="UUID")
    rod_id: int = Field(description="路亚竿ID")
    name: str = Field(description="型号名称")
    sub_name: Optional[str] = Field(default=None, description="子名称")
    total_length: Optional[Dict[str, Any]] = Field(default=None, description="总长")
    pieces: Optional[int] = Field(default=None, description="节数")
    min_lure_wt: Optional[Decimal] = Field(default=None, description="最小饵重")
    max_lure_wt: Optional[Decimal] = Field(default=None, description="最大饵重")
    min_line_wt: Optional[int] = Field(default=None, description="最小线拉力")
    max_line_wt: Optional[int] = Field(default=None, description="最大线拉力")
    min_line_pe: Optional[Decimal] = Field(default=None, description="最小PE线号")
    max_line_pe: Optional[Decimal] = Field(default=None, description="最大PE线号")
    weight: Optional[float] = Field(default=None, description="重量")
    action: Optional[str] = Field(default=None, description="调性")
    power: Optional[str] = Field(default=None, description="硬度")
    grip_material: Optional[str] = Field(default=None, description="手把材料")
    grip_length: Optional[Decimal] = Field(default=None, description="手把长度")
    reel_seat_type: Optional[str] = Field(default=None, description="枪柄还是直柄")
    cover: Optional[str] = Field(default=None, description="型号图片")
    desc: Optional[str] = Field(default=None, description="型号说明")
    short_desc: Optional[str] = Field(default=None, description="一句话描述")
    release_year: Optional[int] = Field(default=None, description="发售年份")


class RodDetailResponse(RodResponse):
    """路亚竿详情响应模型"""
    items: List[RodItemResponse] = Field(default_factory=list, description="型号列表")


class RodListItem(BaseModel):
    """路亚竿列表项模型"""
    id: int = Field(description="路亚竿ID")
    uuid: str = Field(description="UUID")
    name: str = Field(description="路亚竿名称")
    company_name: Optional[str] = Field(default=None, description="品牌名称")
    series_name: Optional[str] = Field(default=None, description="系列名称")
    cover: Optional[str] = Field(default=None, description="封面图片")
    item_count: int = Field(default=0, description="型号数量")
    price_range: Optional[str] = Field(default=None, description="价格区间")
