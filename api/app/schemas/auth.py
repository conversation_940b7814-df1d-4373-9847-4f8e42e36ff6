"""
认证相关的Pydantic模型
用于API请求和响应的数据验证
"""

from typing import Optional
from pydantic import BaseModel, Field, EmailStr


class Token(BaseModel):
    """访问令牌响应模型"""
    access_token: str = Field(description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")


class TokenData(BaseModel):
    """令牌数据模型"""
    username: Optional[str] = Field(default=None, description="用户名")


class UserLogin(BaseModel):
    """用户登录请求模型"""
    username: str = Field(description="用户名、邮箱或手机号")
    password: str = Field(description="密码")


class UserRegister(BaseModel):
    """用户注册请求模型"""
    username: str = Field(min_length=3, max_length=64, description="用户名")
    password: str = Field(min_length=6, max_length=128, description="密码")
    screen_name: Optional[str] = Field(default=None, max_length=30, description="昵称")
    email: Optional[EmailStr] = Field(default=None, description="邮箱")
    phone: Optional[str] = Field(default=None, max_length=20, description="手机号")


class UserProfile(BaseModel):
    """用户资料响应模型"""
    id: int = Field(description="用户ID")
    username: str = Field(description="用户名")
    screen_name: Optional[str] = Field(default=None, description="昵称")
    email: Optional[str] = Field(default=None, description="邮箱")
    phone: Optional[str] = Field(default=None, description="手机号")
    status: int = Field(description="用户状态")
    created_at: int = Field(description="创建时间")
    updated_at: int = Field(description="更新时间")


class UserUpdate(BaseModel):
    """用户信息更新请求模型"""
    screen_name: Optional[str] = Field(default=None, max_length=30, description="昵称")
    email: Optional[EmailStr] = Field(default=None, description="邮箱")
    phone: Optional[str] = Field(default=None, max_length=20, description="手机号")


class PasswordChange(BaseModel):
    """密码修改请求模型"""
    old_password: str = Field(description="原密码")
    new_password: str = Field(min_length=6, max_length=128, description="新密码")


class PasswordReset(BaseModel):
    """密码重置请求模型"""
    email: EmailStr = Field(description="邮箱地址")


class PasswordResetConfirm(BaseModel):
    """密码重置确认请求模型"""
    token: str = Field(description="重置令牌")
    new_password: str = Field(min_length=6, max_length=128, description="新密码")


class EmailVerification(BaseModel):
    """邮箱验证请求模型"""
    token: str = Field(description="验证令牌")


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str = Field(description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    user: UserProfile = Field(description="用户信息")


class RegisterResponse(BaseModel):
    """注册响应模型"""
    message: str = Field(description="注册结果消息")
    user: UserProfile = Field(description="用户信息")
