"""
认证相关的依赖注入函数
"""

from typing import Annotated, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session

from app.core.security import verify_token
from app.crud import user as user_crud
from app.db.session import get_db
from app.models.user import User


# HTTP Bearer 认证方案
security = HTTPBearer()


def get_current_user(
    db: Annotated[Session, Depends(get_db)],
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)]
) -> User:
    """
    获取当前认证用户
    
    Args:
        db: 数据库会话
        credentials: HTTP认证凭据
        
    Returns:
        User: 当前用户对象
        
    Raises:
        HTTPException: 认证失败
    """
    # 验证令牌
    user_id = verify_token(credentials.credentials)
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 获取用户
    user = user_crud.user.get(db, id=int(user_id))
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户状态
    if not user_crud.user.is_active(user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账号已被禁用"
        )
    
    return user


def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    """
    获取当前活跃用户（已验证且未被禁用）
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前活跃用户
        
    Raises:
        HTTPException: 用户未激活
    """
    if not user_crud.user.is_active(current_user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账号已被禁用"
        )
    return current_user


def get_current_superuser(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    """
    获取当前超级用户
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前超级用户
        
    Raises:
        HTTPException: 权限不足
    """
    if not user_crud.user.is_superuser(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    return current_user


def get_optional_current_user(
    db: Annotated[Session, Depends(get_db)],
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[User]:
    """
    获取可选的当前用户（用于可选认证的接口）
    
    Args:
        db: 数据库会话
        credentials: HTTP认证凭据（可选）
        
    Returns:
        Optional[User]: 当前用户对象或None
    """
    if credentials is None:
        return None
    
    try:
        # 验证令牌
        user_id = verify_token(credentials.credentials)
        if user_id is None:
            return None
        
        # 获取用户
        user = user_crud.user.get(db, id=int(user_id))
        if user is None or not user_crud.user.is_active(user):
            return None
        
        return user
    except Exception:
        return None


# 类型注解别名，方便使用
CurrentUser = Annotated[User, Depends(get_current_user)]
CurrentActiveUser = Annotated[User, Depends(get_current_active_user)]
CurrentSuperUser = Annotated[User, Depends(get_current_superuser)]
OptionalCurrentUser = Annotated[Optional[User], Depends(get_optional_current_user)]
