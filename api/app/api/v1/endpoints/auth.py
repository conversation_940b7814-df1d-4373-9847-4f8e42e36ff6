"""
认证相关API接口
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session

from app.db.session import get_db
from app.dependencies.auth import CurrentActiveUser
from app.schemas.auth import (
    UserLogin,
    UserRegister,
    UserProfile,
    UserUpdate,
    PasswordChange,
    PasswordReset,
    PasswordResetConfirm,
    LoginResponse,
    RegisterResponse
)
from app.schemas.common import ResponseModel
from app.services.user_service import user_service


router = APIRouter()


@router.post("/register", response_model=ResponseModel[RegisterResponse])
async def register(
    user_in: UserRegister,
    db: Session = Depends(get_db)
):
    """
    用户注册
    
    - **username**: 用户名（3-64个字符）
    - **password**: 密码（至少6个字符）
    - **screen_name**: 昵称（可选）
    - **email**: 邮箱（可选）
    - **phone**: 手机号（可选）
    """
    result = user_service.register_user(db, user_in=user_in)
    
    return ResponseModel(
        code=200,
        message="注册成功",
        data=result
    )


@router.post("/login", response_model=ResponseModel[LoginResponse])
async def login(
    user_in: UserLogin,
    db: Session = Depends(get_db)
):
    """
    用户登录
    
    - **username**: 用户名、邮箱或手机号
    - **password**: 密码
    """
    result = user_service.authenticate_user(db, user_in=user_in)
    
    return ResponseModel(
        code=200,
        message="登录成功",
        data=result
    )


@router.get("/me", response_model=ResponseModel[UserProfile])
async def get_current_user_profile(
    current_user: CurrentActiveUser
):
    """
    获取当前用户信息
    
    需要认证：Bearer Token
    """
    user_profile = user_service.get_user_profile(current_user)
    
    return ResponseModel(
        code=200,
        message="获取成功",
        data=user_profile
    )


@router.put("/me", response_model=ResponseModel[UserProfile])
async def update_current_user_profile(
    user_in: UserUpdate,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    更新当前用户信息
    
    需要认证：Bearer Token
    
    - **screen_name**: 昵称（可选）
    - **email**: 邮箱（可选）
    - **phone**: 手机号（可选）
    """
    user_profile = user_service.update_user_profile(
        db, user=current_user, user_in=user_in
    )
    
    return ResponseModel(
        code=200,
        message="更新成功",
        data=user_profile
    )


@router.post("/change-password", response_model=ResponseModel[dict])
async def change_password(
    password_in: PasswordChange,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    修改密码
    
    需要认证：Bearer Token
    
    - **old_password**: 原密码
    - **new_password**: 新密码（至少6个字符）
    """
    result = user_service.change_password(
        db, user=current_user, password_in=password_in
    )
    
    return ResponseModel(
        code=200,
        message="密码修改成功",
        data=result
    )


@router.post("/password-reset", response_model=ResponseModel[dict])
async def request_password_reset(
    password_reset: PasswordReset,
    db: Session = Depends(get_db)
):
    """
    请求密码重置
    
    - **email**: 邮箱地址
    
    如果邮箱存在，将发送重置链接到邮箱
    """
    result = user_service.request_password_reset(db, email=password_reset.email)
    
    return ResponseModel(
        code=200,
        message="操作成功",
        data=result
    )


@router.post("/password-reset/confirm", response_model=ResponseModel[dict])
async def confirm_password_reset(
    password_reset_confirm: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """
    确认密码重置
    
    - **token**: 重置令牌
    - **new_password**: 新密码（至少6个字符）
    """
    result = user_service.reset_password(
        db,
        token=password_reset_confirm.token,
        new_password=password_reset_confirm.new_password
    )
    
    return ResponseModel(
        code=200,
        message="密码重置成功",
        data=result
    )
