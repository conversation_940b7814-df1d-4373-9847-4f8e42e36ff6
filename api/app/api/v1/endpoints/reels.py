"""
渔轮API接口
"""

from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session
from app.db.session import get_db
from app.dependencies.auth import CurrentActiveUser
from app.services import reel_service
from app.schemas.reel import (
    ReelSearchParams,
    ReelDetailResponse,
    ReelListItem,
    ReelCreate,
    ReelUpdate,
    ReelResponse
)
from app.schemas.common import PaginatedResponseModel, ResponseModel

router = APIRouter()


@router.get("/search", response_model=PaginatedResponseModel[ReelListItem])
async def search_reels(
    keyword: str = Query(None, description="搜索关键词"),
    company_id: int = Query(None, description="品牌ID"),
    series_id: int = Query(None, description="系列ID"),
    type: str = Query(None, description="渔轮类型"),
    gear_ratio_min: Decimal = Query(None, description="最小速比"),
    gear_ratio_max: Decimal = Query(None, description="最大速比"),
    weight_min: int = Query(None, description="最小重量"),
    weight_max: int = Query(None, description="最大重量"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """
    搜索渔轮
    
    - **keyword**: 搜索关键词，会在渔轮名称、描述中搜索
    - **company_id**: 品牌ID筛选
    - **series_id**: 系列ID筛选
    - **type**: 渔轮类型筛选
    - **gear_ratio_min**: 最小速比筛选
    - **gear_ratio_max**: 最大速比筛选
    - **weight_min**: 最小重量筛选（克）
    - **weight_max**: 最大重量筛选（克）
    - **page**: 页码，从1开始
    - **page_size**: 每页数量，最大100
    """
    search_params = ReelSearchParams(
        keyword=keyword,
        company_id=company_id,
        series_id=series_id,
        type=type,
        gear_ratio_min=gear_ratio_min,
        gear_ratio_max=gear_ratio_max,
        weight_min=weight_min,
        weight_max=weight_max,
        page=page,
        page_size=page_size
    )
    
    reels, pagination = reel_service.search_reels(db, search_params)
    
    return PaginatedResponseModel(
        code=200,
        message="success",
        data=reels,
        pagination=pagination
    )


@router.get("/{reel_id}", response_model=ResponseModel[ReelDetailResponse])
async def get_reel_detail(
    reel_id: int,
    db: Session = Depends(get_db)
):
    """
    获取渔轮详情
    
    - **reel_id**: 渔轮ID
    """
    reel = reel_service.get_reel_detail(db, reel_id)
    
    if not reel:
        raise HTTPException(status_code=404, detail="渔轮不存在")
    
    return ResponseModel(
        code=200,
        message="success",
        data=reel
    )


@router.get("/uuid/{reel_uuid}", response_model=ResponseModel[ReelDetailResponse])
async def get_reel_detail_by_uuid(
    reel_uuid: str,
    db: Session = Depends(get_db)
):
    """
    根据UUID获取渔轮详情
    
    - **reel_uuid**: 渔轮UUID
    """
    reel = reel_service.get_reel_by_uuid(db, reel_uuid)
    
    if not reel:
        raise HTTPException(status_code=404, detail="渔轮不存在")
    
    return ResponseModel(
        code=200,
        message="success",
        data=reel
    )


@router.post("", response_model=ResponseModel[ReelResponse])
async def create_reel(
    reel_in: ReelCreate,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    创建渔轮

    需要认证：Bearer Token

    - **uuid**: UUID
    - **company_id**: 厂商ID（可选）
    - **name**: 名称
    - **type**: 渔轮类型
    - **series_id**: 品牌系列ID（可选）
    - **cover**: 封面图片（可选）
    - **desc**: 描述（可选）
    """
    reel = reel_service.create_reel(db, reel_in=reel_in)

    return ResponseModel(
        code=200,
        message="创建成功",
        data=reel
    )


@router.put("/{reel_id}", response_model=ResponseModel[ReelResponse])
async def update_reel(
    reel_id: int,
    reel_in: ReelUpdate,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    更新渔轮

    需要认证：Bearer Token

    - **reel_id**: 渔轮ID
    - **uuid**: UUID（可选）
    - **company_id**: 厂商ID（可选）
    - **name**: 名称（可选）
    - **type**: 渔轮类型（可选）
    - **series_id**: 品牌系列ID（可选）
    - **cover**: 封面图片（可选）
    - **desc**: 描述（可选）
    """
    reel = reel_service.update_reel(db, reel_id=reel_id, reel_in=reel_in)

    if not reel:
        raise HTTPException(status_code=404, detail="渔轮不存在")

    return ResponseModel(
        code=200,
        message="更新成功",
        data=reel
    )


@router.delete("/{reel_id}", response_model=ResponseModel[dict])
async def delete_reel(
    reel_id: int,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    删除渔轮

    需要认证：Bearer Token

    - **reel_id**: 渔轮ID
    """
    success = reel_service.delete_reel(db, reel_id=reel_id)

    if not success:
        raise HTTPException(status_code=404, detail="渔轮不存在")

    return ResponseModel(
        code=200,
        message="删除成功",
        data={"deleted": True}
    )
