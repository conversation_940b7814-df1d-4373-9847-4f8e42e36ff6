"""
鱼种API接口
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlmodel import Session

from app.db.session import get_db
from app.dependencies.auth import CurrentActiveUser
from app.schemas.fish import (
    FishSearchParams,
    FishCreate,
    FishUpdate,
    FishResponse,
    FishListItem,
    FishDetailResponse
)
from app.schemas.common import PaginatedResponseModel, ResponseModel
from app.services.fish_service import fish_service


router = APIRouter()


@router.get("/search", response_model=PaginatedResponseModel[FishListItem])
async def search_fish(
    keyword: str = Query(None, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """
    搜索鱼种
    
    - **keyword**: 搜索关键词，会在鱼种名称、别名中搜索
    - **page**: 页码，从1开始
    - **page_size**: 每页数量，最大100
    """
    search_params = FishSearchParams(
        keyword=keyword,
        page=page,
        page_size=page_size
    )
    
    fish_list, pagination = fish_service.search_fish(db, search_params)
    
    return PaginatedResponseModel(
        code=200,
        message="success",
        data=fish_list,
        pagination=pagination
    )


@router.get("/popular", response_model=ResponseModel[List[FishListItem]])
async def get_popular_fish(
    limit: int = Query(10, ge=1, le=50, description="限制数量"),
    db: Session = Depends(get_db)
):
    """
    获取热门鱼种
    
    - **limit**: 限制数量，最大50
    """
    fish_list = fish_service.get_popular_fish(db, limit=limit)
    
    return ResponseModel(
        code=200,
        message="success",
        data=fish_list
    )


@router.get("/{fish_id}", response_model=ResponseModel[FishDetailResponse])
async def get_fish_detail(
    fish_id: int,
    db: Session = Depends(get_db)
):
    """
    获取鱼种详情
    
    - **fish_id**: 鱼种ID
    """
    fish = fish_service.get_fish_detail(db, fish_id)
    
    if not fish:
        raise HTTPException(status_code=404, detail="鱼种不存在")
    
    return ResponseModel(
        code=200,
        message="success",
        data=fish
    )


@router.post("", response_model=ResponseModel[FishResponse])
async def create_fish(
    fish_in: FishCreate,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    创建鱼种
    
    需要认证：Bearer Token
    
    - **name**: 鱼种名称
    - **alias_name**: 别名（可选）
    """
    fish = fish_service.create_fish(db, fish_in=fish_in)
    
    return ResponseModel(
        code=200,
        message="创建成功",
        data=fish
    )


@router.put("/{fish_id}", response_model=ResponseModel[FishResponse])
async def update_fish(
    fish_id: int,
    fish_in: FishUpdate,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    更新鱼种
    
    需要认证：Bearer Token
    
    - **fish_id**: 鱼种ID
    - **name**: 鱼种名称（可选）
    - **alias_name**: 别名（可选）
    """
    fish = fish_service.update_fish(db, fish_id=fish_id, fish_in=fish_in)
    
    if not fish:
        raise HTTPException(status_code=404, detail="鱼种不存在")
    
    return ResponseModel(
        code=200,
        message="更新成功",
        data=fish
    )


@router.delete("/{fish_id}", response_model=ResponseModel[dict])
async def delete_fish(
    fish_id: int,
    current_user: CurrentActiveUser,
    db: Session = Depends(get_db)
):
    """
    删除鱼种
    
    需要认证：Bearer Token
    
    - **fish_id**: 鱼种ID
    """
    success = fish_service.delete_fish(db, fish_id=fish_id)
    
    if not success:
        raise HTTPException(status_code=404, detail="鱼种不存在")
    
    return ResponseModel(
        code=200,
        message="删除成功",
        data={"deleted": True}
    )
