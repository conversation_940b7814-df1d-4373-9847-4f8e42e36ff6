"""
API v1 路由聚合
"""

from fastapi import APIRouter
from app.api.v1.endpoints import fishing_locations, rods, reels, fish, auth

api_router = APIRouter()

# 路亚基地相关接口
api_router.include_router(
    fishing_locations.router,
    prefix="/fishing-locations",
    tags=["路亚基地"]
)

# 路亚竿相关接口
api_router.include_router(
    rods.router,
    prefix="/rods",
    tags=["路亚竿"]
)

# 渔轮相关接口
api_router.include_router(
    reels.router,
    prefix="/reels",
    tags=["渔轮"]
)

# 鱼种相关接口
api_router.include_router(
    fish.router,
    prefix="/fish",
    tags=["鱼种"]
)

# 认证相关接口
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证"]
)
