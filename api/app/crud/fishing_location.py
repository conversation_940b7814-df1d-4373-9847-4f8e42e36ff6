"""
路亚基地CRUD操作
"""

from typing import List, Optional
from sqlmodel import Session, select, or_, and_
from app.crud.base import CRUDBase
from app.models.fishing_location import FishingLocation, FishingLocationCreate, FishingLocationUpdate


class CRUDFishingLocation(CRUDBase[FishingLocation, FishingLocationCreate, FishingLocationUpdate]):
    """路亚基地CRUD操作类"""
    
    def search(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        province_code: Optional[str] = None,
        city_code: Optional[str] = None,
        area_code: Optional[str] = None,
        types: Optional[List[str]] = None,
        fish_species: Optional[List[str]] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[FishingLocation]:
        """搜索路亚基地"""
        statement = select(FishingLocation)
        
        # 关键词搜索
        if keyword:
            statement = statement.where(
                or_(
                    FishingLocation.name.contains(keyword),
                    FishingLocation.desc.contains(keyword),
                    FishingLocation.contact.contains(keyword)
                )
            )
        
        # 地区筛选
        if province_code:
            statement = statement.where(FishingLocation.province_code == province_code)
        if city_code:
            statement = statement.where(FishingLocation.city_code == city_code)
        if area_code:
            statement = statement.where(FishingLocation.area_code == area_code)
        
        # 类型筛选 - 这里需要根据JSON字段的具体实现来调整
        # if types:
        #     # JSON字段查询需要特殊处理，这里先注释
        #     pass
        
        # 鱼种筛选 - 同样需要根据JSON字段的具体实现来调整
        # if fish_species:
        #     # JSON字段查询需要特殊处理，这里先注释
        #     pass
        
        statement = statement.offset(skip).limit(limit)
        return db.exec(statement).all()
    
    def get_by_name(self, db: Session, name: str) -> Optional[FishingLocation]:
        """根据名称获取路亚基地"""
        return self.get_by_field(db, "name", name)
    
    def get_by_region(
        self,
        db: Session,
        province_code: Optional[str] = None,
        city_code: Optional[str] = None,
        area_code: Optional[str] = None,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[FishingLocation]:
        """根据地区获取路亚基地"""
        statement = select(FishingLocation)
        
        conditions = []
        if province_code:
            conditions.append(FishingLocation.province_code == province_code)
        if city_code:
            conditions.append(FishingLocation.city_code == city_code)
        if area_code:
            conditions.append(FishingLocation.area_code == area_code)
        
        if conditions:
            statement = statement.where(and_(*conditions))
        
        statement = statement.offset(skip).limit(limit)
        return db.exec(statement).all()
    
    def count_search(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        province_code: Optional[str] = None,
        city_code: Optional[str] = None,
        area_code: Optional[str] = None,
        types: Optional[List[str]] = None,
        fish_species: Optional[List[str]] = None
    ) -> int:
        """统计搜索结果数量"""
        statement = select(FishingLocation)
        
        # 关键词搜索
        if keyword:
            statement = statement.where(
                or_(
                    FishingLocation.name.contains(keyword),
                    FishingLocation.desc.contains(keyword),
                    FishingLocation.contact.contains(keyword)
                )
            )
        
        # 地区筛选
        if province_code:
            statement = statement.where(FishingLocation.province_code == province_code)
        if city_code:
            statement = statement.where(FishingLocation.city_code == city_code)
        if area_code:
            statement = statement.where(FishingLocation.area_code == area_code)
        
        return len(db.exec(statement).all())


# 创建CRUD实例
fishing_location = CRUDFishingLocation(FishingLocation)
