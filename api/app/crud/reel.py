"""
渔轮CRUD操作
"""

from typing import List, Optional
from decimal import Decimal
from sqlmodel import Session, select, or_, and_
from app.crud.base import CRUDBase
from app.models.reel import Reel, ReelCreate, ReelUpdate
from app.models.reel_item import ReelItem, ReelItemCreate, ReelItemUpdate


class CRUDReel(CRUDBase[Reel, ReelCreate, ReelUpdate]):
    """渔轮CRUD操作类"""
    
    def search(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        company_id: Optional[int] = None,
        series_id: Optional[int] = None,
        type: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reel]:
        """搜索渔轮"""
        statement = select(Reel)
        
        # 关键词搜索
        if keyword:
            statement = statement.where(
                or_(
                    Reel.name.contains(keyword),
                    Reel.desc.contains(keyword)
                )
            )
        
        # 品牌筛选
        if company_id:
            statement = statement.where(Reel.company_id == company_id)
        
        # 系列筛选
        if series_id:
            statement = statement.where(Reel.series_id == series_id)
        
        # 类型筛选
        if type:
            statement = statement.where(Reel.type == type)
        
        statement = statement.offset(skip).limit(limit)
        return db.exec(statement).all()
    
    def get_by_uuid(self, db: Session, uuid: str) -> Optional[Reel]:
        """根据UUID获取渔轮"""
        return self.get_by_field(db, "uuid", uuid)
    
    def get_by_company(
        self,
        db: Session,
        company_id: int,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reel]:
        """根据品牌获取渔轮"""
        return self.get_multi_by_field(db, "company_id", company_id, skip=skip, limit=limit)
    
    def get_by_series(
        self,
        db: Session,
        series_id: int,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reel]:
        """根据系列获取渔轮"""
        return self.get_multi_by_field(db, "series_id", series_id, skip=skip, limit=limit)
    
    def get_by_type(
        self,
        db: Session,
        type: str,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[Reel]:
        """根据类型获取渔轮"""
        return self.get_multi_by_field(db, "type", type, skip=skip, limit=limit)


class CRUDReelItem(CRUDBase[ReelItem, ReelItemCreate, ReelItemUpdate]):
    """渔轮型号CRUD操作类"""
    
    def search(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        reel_id: Optional[int] = None,
        gear_ratio_min: Optional[Decimal] = None,
        gear_ratio_max: Optional[Decimal] = None,
        weight_min: Optional[int] = None,
        weight_max: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[ReelItem]:
        """搜索渔轮型号"""
        statement = select(ReelItem)
        
        # 关键词搜索
        if keyword:
            statement = statement.where(
                or_(
                    ReelItem.name.contains(keyword),
                    ReelItem.desc.contains(keyword),
                    ReelItem.short_desc.contains(keyword)
                )
            )
        
        # 渔轮筛选
        if reel_id:
            statement = statement.where(ReelItem.reel_id == reel_id)
        
        # 速比筛选
        if gear_ratio_min is not None:
            statement = statement.where(ReelItem.gear_ratio >= gear_ratio_min)
        if gear_ratio_max is not None:
            statement = statement.where(ReelItem.gear_ratio <= gear_ratio_max)
        
        # 重量筛选
        if weight_min is not None:
            statement = statement.where(ReelItem.weight >= weight_min)
        if weight_max is not None:
            statement = statement.where(ReelItem.weight <= weight_max)
        
        statement = statement.offset(skip).limit(limit)
        return db.exec(statement).all()
    
    def get_by_reel_id(
        self,
        db: Session,
        reel_id: int,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[ReelItem]:
        """根据渔轮ID获取型号列表"""
        return self.get_multi_by_field(db, "reel_id", reel_id, skip=skip, limit=limit)
    
    def get_by_uuid(self, db: Session, uuid: str) -> Optional[ReelItem]:
        """根据UUID获取渔轮型号"""
        return self.get_by_field(db, "uuid", uuid)


# 创建CRUD实例
reel = CRUDReel(Reel)
reel_item = CRUDReelItem(ReelItem)
