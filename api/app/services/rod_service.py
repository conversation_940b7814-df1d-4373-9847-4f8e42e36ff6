"""
路亚竿业务逻辑服务
"""

from typing import List, Optional, Tuple
from fastapi import HTTPException, status
from sqlmodel import Session
from app.crud import rod, rod_item
from app.schemas.rod import (
    RodSearchParams,
    RodResponse,
    RodDetailResponse,
    RodListItem,
    RodItemResponse,
    RodCreate,
    RodUpdate
)
from app.schemas.common import PaginationModel


class RodService:
    """路亚竿业务逻辑服务类"""
    
    def search_rods(
        self,
        db: Session,
        search_params: RodSearchParams
    ) -> Tuple[List[RodListItem], PaginationModel]:
        """
        搜索路亚竿
        
        Args:
            db: 数据库会话
            search_params: 搜索参数
            
        Returns:
            Tuple[List[RodListItem], PaginationModel]: 路亚竿列表和分页信息
        """
        # 计算偏移量
        skip = (search_params.page - 1) * search_params.page_size
        
        # 搜索路亚竿
        rods = rod.search(
            db,
            keyword=search_params.keyword,
            company_id=search_params.company_id,
            series_id=search_params.series_id,
            skip=skip,
            limit=search_params.page_size
        )
        
        # 统计总数 - 这里简化处理，实际应该有专门的计数方法
        all_rods = rod.search(
            db,
            keyword=search_params.keyword,
            company_id=search_params.company_id,
            series_id=search_params.series_id,
            skip=0,
            limit=10000
        )
        total = len(all_rods)
        
        # 转换为响应模型
        rod_items = []
        for rod_obj in rods:
            # 获取型号数量
            items = rod_item.get_by_rod_id(db, rod_obj.id, skip=0, limit=1000)
            item_count = len(items)
            
            rod_items.append(RodListItem(
                id=rod_obj.id,
                uuid=rod_obj.uuid,
                name=rod_obj.name,
                company_name=None,  # 这里需要关联查询品牌名称
                series_name=None,   # 这里需要关联查询系列名称
                cover=rod_obj.cover,
                item_count=item_count,
                price_range=None    # 价格区间需要根据业务逻辑计算
            ))
        
        # 计算分页信息
        total_pages = (total + search_params.page_size - 1) // search_params.page_size
        pagination = PaginationModel(
            page=search_params.page,
            page_size=search_params.page_size,
            total=total,
            total_pages=total_pages
        )
        
        return rod_items, pagination
    
    def get_rod_detail(
        self,
        db: Session,
        rod_id: int
    ) -> Optional[RodDetailResponse]:
        """
        获取路亚竿详情
        
        Args:
            db: 数据库会话
            rod_id: 路亚竿ID
            
        Returns:
            Optional[RodDetailResponse]: 路亚竿详情
        """
        rod_obj = rod.get(db, rod_id)
        if not rod_obj:
            return None
        
        # 获取所有型号
        items = rod_item.get_by_rod_id(db, rod_id, skip=0, limit=1000)
        
        # 转换型号数据
        item_responses = [
            RodItemResponse(
                id=item.id,
                uuid=item.uuid,
                rod_id=item.rod_id,
                name=item.name,
                sub_name=item.sub_name,
                total_length=item.total_length,
                pieces=item.pieces,
                min_lure_wt=item.min_lure_wt,
                max_lure_wt=item.max_lure_wt,
                min_line_wt=item.min_line_wt,
                max_line_wt=item.max_line_wt,
                min_line_pe=item.min_line_pe,
                max_line_pe=item.max_line_pe,
                weight=item.weight,
                action=item.action,
                power=item.power,
                grip_material=item.grip_material,
                grip_length=item.grip_length,
                reel_seat_type=item.reel_seat_type,
                cover=item.cover,
                desc=item.desc,
                short_desc=item.short_desc,
                release_year=item.release_year
            )
            for item in items
        ]
        
        return RodDetailResponse(
            id=rod_obj.id,
            uuid=rod_obj.uuid,
            name=rod_obj.name,
            company_id=rod_obj.company_id,
            company_name=None,  # 需要关联查询
            series_id=rod_obj.series_id,
            series_name=None,   # 需要关联查询
            cover=rod_obj.cover,
            desc=rod_obj.desc,
            items=item_responses
        )
    
    def get_rod_by_uuid(
        self,
        db: Session,
        rod_uuid: str
    ) -> Optional[RodDetailResponse]:
        """
        根据UUID获取路亚竿详情
        
        Args:
            db: 数据库会话
            rod_uuid: 路亚竿UUID
            
        Returns:
            Optional[RodDetailResponse]: 路亚竿详情
        """
        rod_obj = rod.get_by_uuid(db, rod_uuid)
        if not rod_obj:
            return None
        
        return self.get_rod_detail(db, rod_obj.id)

    def create_rod(
        self,
        db: Session,
        rod_in: RodCreate
    ) -> RodResponse:
        """
        创建路亚竿

        Args:
            db: 数据库会话
            rod_in: 路亚竿创建信息

        Returns:
            RodResponse: 创建的路亚竿信息

        Raises:
            HTTPException: UUID已存在
        """
        # 检查UUID是否已存在
        existing_rod = rod.get_by_uuid(db, uuid=rod_in.uuid)
        if existing_rod:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="UUID已存在"
            )

        # 创建路亚竿
        rod_obj = rod.create(db, obj_in=rod_in)

        return RodResponse(
            id=rod_obj.id,
            uuid=rod_obj.uuid,
            name=rod_obj.name,
            company_id=rod_obj.company_id,
            series_id=rod_obj.series_id,
            cover=rod_obj.cover,
            desc=rod_obj.desc
        )

    def update_rod(
        self,
        db: Session,
        rod_id: int,
        rod_in: RodUpdate
    ) -> Optional[RodResponse]:
        """
        更新路亚竿

        Args:
            db: 数据库会话
            rod_id: 路亚竿ID
            rod_in: 路亚竿更新信息

        Returns:
            Optional[RodResponse]: 更新后的路亚竿信息

        Raises:
            HTTPException: UUID已被其他路亚竿使用
        """
        # 获取路亚竿
        rod_obj = rod.get(db, id=rod_id)
        if not rod_obj:
            return None

        # 检查UUID是否被其他路亚竿使用
        if rod_in.uuid and rod_in.uuid != rod_obj.uuid:
            existing_rod = rod.get_by_uuid(db, uuid=rod_in.uuid)
            if existing_rod and existing_rod.id != rod_obj.id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="UUID已被其他路亚竿使用"
                )

        # 更新路亚竿
        updated_rod = rod.update(db, db_obj=rod_obj, obj_in=rod_in)

        return RodResponse(
            id=updated_rod.id,
            uuid=updated_rod.uuid,
            name=updated_rod.name,
            company_id=updated_rod.company_id,
            series_id=updated_rod.series_id,
            cover=updated_rod.cover,
            desc=updated_rod.desc
        )

    def delete_rod(
        self,
        db: Session,
        rod_id: int
    ) -> bool:
        """
        删除路亚竿

        Args:
            db: 数据库会话
            rod_id: 路亚竿ID

        Returns:
            bool: 是否删除成功
        """
        rod_obj = rod.get(db, id=rod_id)
        if not rod_obj:
            return False

        # 删除相关的型号记录
        rod_items = rod_item.get_by_rod_id(db, rod_id=rod_id)
        for item in rod_items:
            rod_item.remove(db, id=item.id)

        # 删除路亚竿
        rod.remove(db, id=rod_id)
        return True


# 创建服务实例
rod_service = RodService()
