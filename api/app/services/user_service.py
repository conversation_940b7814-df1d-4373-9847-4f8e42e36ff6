"""
用户业务逻辑服务
"""

from typing import Optional

from fastapi import HTTPException, status
from sqlmodel import Session

from app.crud import user as user_crud
from app.models.user import User
from app.schemas.auth import (
    UserRegister, 
    UserLogin, 
    UserProfile, 
    UserUpdate,
    PasswordChange,
    LoginResponse,
    RegisterResponse
)
from app.core.security import (
    create_access_token,
    verify_password,
    generate_password_reset_token,
    verify_password_reset_token,
    generate_verification_token
)


class UserService:
    """用户服务类"""

    def register_user(
        self, db: Session, *, user_in: UserRegister
    ) -> RegisterResponse:
        """
        用户注册
        
        Args:
            db: 数据库会话
            user_in: 注册信息
            
        Returns:
            RegisterResponse: 注册响应
            
        Raises:
            HTTPException: 用户名、邮箱或手机号已存在
        """
        # 检查用户名是否已存在
        if user_crud.user.get_by_username(db, username=user_in.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 检查邮箱是否已存在
        if user_in.email and user_crud.user.get_by_email(db, email=user_in.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        
        # 检查手机号是否已存在
        if user_in.phone and user_crud.user.get_by_phone(db, phone=user_in.phone):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号已被注册"
            )
        
        # 创建用户
        user = user_crud.user.create(db, obj_in=user_in)
        
        # 构建用户资料
        user_profile = UserProfile(
            id=user.id,
            username=user.username,
            screen_name=user.screen_name,
            email=user.email,
            phone=user.phone,
            status=user.status,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        
        return RegisterResponse(
            message="注册成功",
            user=user_profile
        )

    def authenticate_user(
        self, db: Session, *, user_in: UserLogin
    ) -> LoginResponse:
        """
        用户登录认证
        
        Args:
            db: 数据库会话
            user_in: 登录信息
            
        Returns:
            LoginResponse: 登录响应
            
        Raises:
            HTTPException: 用户名或密码错误，或用户被禁用
        """
        user = user_crud.user.authenticate(
            db, username=user_in.username, password=user_in.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        if not user_crud.user.is_active(user):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户账号已被禁用"
            )
        
        # 生成访问令牌
        access_token = create_access_token(subject=user.id)
        
        # 构建用户资料
        user_profile = UserProfile(
            id=user.id,
            username=user.username,
            screen_name=user.screen_name,
            email=user.email,
            phone=user.phone,
            status=user.status,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user=user_profile
        )

    def get_user_profile(self, user: User) -> UserProfile:
        """
        获取用户资料
        
        Args:
            user: 用户对象
            
        Returns:
            UserProfile: 用户资料
        """
        return UserProfile(
            id=user.id,
            username=user.username,
            screen_name=user.screen_name,
            email=user.email,
            phone=user.phone,
            status=user.status,
            created_at=user.created_at,
            updated_at=user.updated_at
        )

    def update_user_profile(
        self, db: Session, *, user: User, user_in: UserUpdate
    ) -> UserProfile:
        """
        更新用户资料
        
        Args:
            db: 数据库会话
            user: 当前用户
            user_in: 更新信息
            
        Returns:
            UserProfile: 更新后的用户资料
            
        Raises:
            HTTPException: 邮箱或手机号已被其他用户使用
        """
        # 检查邮箱是否被其他用户使用
        if user_in.email and user_in.email != user.email:
            existing_user = user_crud.user.get_by_email(db, email=user_in.email)
            if existing_user and existing_user.id != user.id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已被其他用户使用"
                )
        
        # 检查手机号是否被其他用户使用
        if user_in.phone and user_in.phone != user.phone:
            existing_user = user_crud.user.get_by_phone(db, phone=user_in.phone)
            if existing_user and existing_user.id != user.id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="手机号已被其他用户使用"
                )
        
        # 更新用户信息
        updated_user = user_crud.user.update(db, db_obj=user, obj_in=user_in)
        
        return self.get_user_profile(updated_user)

    def change_password(
        self, db: Session, *, user: User, password_in: PasswordChange
    ) -> dict:
        """
        修改密码
        
        Args:
            db: 数据库会话
            user: 当前用户
            password_in: 密码修改信息
            
        Returns:
            dict: 操作结果
            
        Raises:
            HTTPException: 原密码错误
        """
        # 验证原密码
        if not verify_password(password_in.old_password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="原密码错误"
            )
        
        # 更新密码
        user_crud.user.update_password(
            db, user=user, new_password=password_in.new_password
        )
        
        return {"message": "密码修改成功"}

    def request_password_reset(self, db: Session, *, email: str) -> dict:
        """
        请求密码重置
        
        Args:
            db: 数据库会话
            email: 邮箱地址
            
        Returns:
            dict: 操作结果
        """
        user = user_crud.user.get_by_email(db, email=email)
        if not user:
            # 为了安全，即使邮箱不存在也返回成功消息
            return {"message": "如果邮箱存在，重置链接已发送"}
        
        # 生成重置令牌
        reset_token = generate_password_reset_token(email)
        user_crud.user.set_password_reset_token(db, user=user, token=reset_token)
        
        # 这里应该发送邮件，暂时省略
        # send_password_reset_email(email, reset_token)
        
        return {"message": "如果邮箱存在，重置链接已发送"}

    def reset_password(
        self, db: Session, *, token: str, new_password: str
    ) -> dict:
        """
        重置密码
        
        Args:
            db: 数据库会话
            token: 重置令牌
            new_password: 新密码
            
        Returns:
            dict: 操作结果
            
        Raises:
            HTTPException: 令牌无效或已过期
        """
        email = verify_password_reset_token(token)
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="重置令牌无效或已过期"
            )
        
        user = user_crud.user.get_by_email(db, email=email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 更新密码并清除重置令牌
        user_crud.user.update_password(db, user=user, new_password=new_password)
        user_crud.user.set_password_reset_token(db, user=user, token=None)
        
        return {"message": "密码重置成功"}


# 创建用户服务实例
user_service = UserService()
