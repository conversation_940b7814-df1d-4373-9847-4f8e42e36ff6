"""
渔轮业务逻辑服务
"""

from typing import List, Optional, Tuple
from sqlmodel import Session
from app.crud import reel, reel_item
from app.schemas.reel import (
    ReelSearchParams,
    ReelResponse,
    ReelDetailResponse,
    ReelListItem,
    ReelItemResponse
)
from app.schemas.common import PaginationModel


class ReelService:
    """渔轮业务逻辑服务类"""
    
    def search_reels(
        self,
        db: Session,
        search_params: ReelSearchParams
    ) -> Tuple[List[ReelListItem], PaginationModel]:
        """
        搜索渔轮
        
        Args:
            db: 数据库会话
            search_params: 搜索参数
            
        Returns:
            Tuple[List[ReelListItem], PaginationModel]: 渔轮列表和分页信息
        """
        # 计算偏移量
        skip = (search_params.page - 1) * search_params.page_size
        
        # 搜索渔轮
        reels = reel.search(
            db,
            keyword=search_params.keyword,
            company_id=search_params.company_id,
            series_id=search_params.series_id,
            type=search_params.type,
            skip=skip,
            limit=search_params.page_size
        )
        
        # 统计总数 - 这里简化处理，实际应该有专门的计数方法
        all_reels = reel.search(
            db,
            keyword=search_params.keyword,
            company_id=search_params.company_id,
            series_id=search_params.series_id,
            type=search_params.type,
            skip=0,
            limit=10000
        )
        total = len(all_reels)
        
        # 转换为响应模型
        reel_items = []
        for reel_obj in reels:
            # 获取型号数量
            items = reel_item.get_by_reel_id(db, reel_obj.id, skip=0, limit=1000)
            item_count = len(items)
            
            reel_items.append(ReelListItem(
                id=reel_obj.id,
                uuid=reel_obj.uuid,
                name=reel_obj.name,
                type=reel_obj.type,
                company_name=None,  # 这里需要关联查询品牌名称
                series_name=None,   # 这里需要关联查询系列名称
                cover=reel_obj.cover,
                item_count=item_count,
                price_range=None    # 价格区间需要根据业务逻辑计算
            ))
        
        # 计算分页信息
        total_pages = (total + search_params.page_size - 1) // search_params.page_size
        pagination = PaginationModel(
            page=search_params.page,
            page_size=search_params.page_size,
            total=total,
            total_pages=total_pages
        )
        
        return reel_items, pagination
    
    def get_reel_detail(
        self,
        db: Session,
        reel_id: int
    ) -> Optional[ReelDetailResponse]:
        """
        获取渔轮详情
        
        Args:
            db: 数据库会话
            reel_id: 渔轮ID
            
        Returns:
            Optional[ReelDetailResponse]: 渔轮详情
        """
        reel_obj = reel.get(db, reel_id)
        if not reel_obj:
            return None
        
        # 获取所有型号
        items = reel_item.get_by_reel_id(db, reel_id, skip=0, limit=1000)
        
        # 转换型号数据
        item_responses = [
            ReelItemResponse(
                id=item.id,
                uuid=item.uuid,
                reel_id=item.reel_id,
                name=item.name,
                gear_ratio=item.gear_ratio,
                cover=item.cover,
                desc=item.desc,
                short_desc=item.short_desc,
                best_drag=item.best_drag,
                max_drag=item.max_drag,
                weight=item.weight,
                spool=item.spool,
                bearings=item.bearings,
                nylon_capacity=item.nylon_capacity,
                pe_capacity=item.pe_capacity,
                fluorocarbon_capacity=item.fluorocarbon_capacity,
                line_per_crank=item.line_per_crank,
                handle_length=item.handle_length,
                handle_position=item.handle_position,
                release_year=item.release_year
            )
            for item in items
        ]
        
        return ReelDetailResponse(
            id=reel_obj.id,
            uuid=reel_obj.uuid,
            name=reel_obj.name,
            type=reel_obj.type,
            company_id=reel_obj.company_id,
            company_name=None,  # 需要关联查询
            series_id=reel_obj.series_id,
            series_name=None,   # 需要关联查询
            cover=reel_obj.cover,
            desc=reel_obj.desc,
            items=item_responses
        )
    
    def get_reel_by_uuid(
        self,
        db: Session,
        reel_uuid: str
    ) -> Optional[ReelDetailResponse]:
        """
        根据UUID获取渔轮详情
        
        Args:
            db: 数据库会话
            reel_uuid: 渔轮UUID
            
        Returns:
            Optional[ReelDetailResponse]: 渔轮详情
        """
        reel_obj = reel.get_by_uuid(db, reel_uuid)
        if not reel_obj:
            return None
        
        return self.get_reel_detail(db, reel_obj.id)


# 创建服务实例
reel_service = ReelService()
