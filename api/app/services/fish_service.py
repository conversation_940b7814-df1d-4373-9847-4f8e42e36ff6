"""
鱼种业务逻辑服务
"""

from typing import List, Optional, Tu<PERSON>

from fastapi import HTTPException, status
from sqlmodel import Session

from app.crud import fish as fish_crud
from app.schemas.fish import (
    FishSearchParams,
    FishCreate,
    FishUpdate,
    FishResponse,
    FishListItem,
    FishDetailResponse
)
from app.schemas.common import PaginationModel


class FishService:
    """鱼种服务类"""

    def search_fish(
        self,
        db: Session,
        search_params: FishSearchParams
    ) -> Tuple[List[FishListItem], PaginationModel]:
        """
        搜索鱼种
        
        Args:
            db: 数据库会话
            search_params: 搜索参数
            
        Returns:
            Tuple[List[FishListItem], PaginationModel]: 鱼种列表和分页信息
        """
        # 计算偏移量
        skip = (search_params.page - 1) * search_params.page_size
        
        # 搜索鱼种
        fish_list = fish_crud.search(
            db,
            keyword=search_params.keyword,
            skip=skip,
            limit=search_params.page_size
        )

        # 统计总数
        total = fish_crud.count_search(
            db,
            keyword=search_params.keyword
        )
        
        # 转换为响应模型
        fish_items = [
            FishListItem(
                id=fish.id,
                name=fish.name,
                alias_name=fish.alias_name
            )
            for fish in fish_list
        ]
        
        # 计算分页信息
        total_pages = (total + search_params.page_size - 1) // search_params.page_size
        pagination = PaginationModel(
            page=search_params.page,
            page_size=search_params.page_size,
            total=total,
            total_pages=total_pages
        )
        
        return fish_items, pagination

    def get_fish_detail(
        self,
        db: Session,
        fish_id: int
    ) -> Optional[FishDetailResponse]:
        """
        获取鱼种详情
        
        Args:
            db: 数据库会话
            fish_id: 鱼种ID
            
        Returns:
            Optional[FishDetailResponse]: 鱼种详情
        """
        fish = fish_crud.get(db, id=fish_id)
        if not fish:
            return None
        
        return FishDetailResponse(
            id=fish.id,
            name=fish.name,
            alias_name=fish.alias_name
        )

    def create_fish(
        self,
        db: Session,
        fish_in: FishCreate
    ) -> FishResponse:
        """
        创建鱼种
        
        Args:
            db: 数据库会话
            fish_in: 鱼种创建信息
            
        Returns:
            FishResponse: 创建的鱼种信息
            
        Raises:
            HTTPException: 鱼种名称已存在
        """
        # 检查名称是否已存在
        existing_fish = fish_crud.get_by_name(db, name=fish_in.name)
        if existing_fish:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="鱼种名称已存在"
            )

        # 检查别名是否已存在
        if fish_in.alias_name:
            existing_fish = fish_crud.get_by_alias_name(db, alias_name=fish_in.alias_name)
            if existing_fish:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="鱼种别名已存在"
                )

        # 创建鱼种
        fish = fish_crud.create(db, obj_in=fish_in)
        
        return FishResponse(
            id=fish.id,
            name=fish.name,
            alias_name=fish.alias_name
        )

    def update_fish(
        self,
        db: Session,
        fish_id: int,
        fish_in: FishUpdate
    ) -> Optional[FishResponse]:
        """
        更新鱼种
        
        Args:
            db: 数据库会话
            fish_id: 鱼种ID
            fish_in: 鱼种更新信息
            
        Returns:
            Optional[FishResponse]: 更新后的鱼种信息
            
        Raises:
            HTTPException: 鱼种不存在或名称/别名已被其他鱼种使用
        """
        # 获取鱼种
        fish = fish_crud.get(db, id=fish_id)
        if not fish:
            return None

        # 检查名称是否被其他鱼种使用
        if fish_in.name and fish_in.name != fish.name:
            existing_fish = fish_crud.get_by_name(db, name=fish_in.name)
            if existing_fish and existing_fish.id != fish.id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="鱼种名称已被其他鱼种使用"
                )

        # 检查别名是否被其他鱼种使用
        if fish_in.alias_name and fish_in.alias_name != fish.alias_name:
            existing_fish = fish_crud.get_by_alias_name(db, alias_name=fish_in.alias_name)
            if existing_fish and existing_fish.id != fish.id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="鱼种别名已被其他鱼种使用"
                )

        # 更新鱼种
        updated_fish = fish_crud.update(db, db_obj=fish, obj_in=fish_in)
        
        return FishResponse(
            id=updated_fish.id,
            name=updated_fish.name,
            alias_name=updated_fish.alias_name
        )

    def delete_fish(
        self,
        db: Session,
        fish_id: int
    ) -> bool:
        """
        删除鱼种
        
        Args:
            db: 数据库会话
            fish_id: 鱼种ID
            
        Returns:
            bool: 是否删除成功
        """
        fish = fish_crud.get(db, id=fish_id)
        if not fish:
            return False

        fish_crud.remove(db, id=fish_id)
        return True

    def get_popular_fish(
        self,
        db: Session,
        limit: int = 10
    ) -> List[FishListItem]:
        """
        获取热门鱼种
        
        Args:
            db: 数据库会话
            limit: 限制数量
            
        Returns:
            List[FishListItem]: 热门鱼种列表
        """
        fish_list = fish_crud.get_popular(db, limit=limit)
        
        return [
            FishListItem(
                id=fish.id,
                name=fish.name,
                alias_name=fish.alias_name
            )
            for fish in fish_list
        ]


# 创建鱼种服务实例
fish_service = FishService()
