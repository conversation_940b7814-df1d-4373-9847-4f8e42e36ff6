"""
主应用API测试
"""

from fastapi.testclient import TestClient


def test_root_endpoint(client: TestClient):
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "docs" in data


def test_health_check(client: TestClient):
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "app_name" in data
    assert "version" in data


def test_api_docs(client: TestClient):
    """测试API文档"""
    response = client.get("/api/v1/docs")
    assert response.status_code == 200


def test_openapi_json(client: TestClient):
    """测试OpenAPI JSON"""
    response = client.get("/api/v1/openapi.json")
    assert response.status_code == 200
    data = response.json()
    assert "openapi" in data
    assert "info" in data
    assert "paths" in data
