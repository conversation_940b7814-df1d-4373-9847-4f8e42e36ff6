"""
Pytest配置文件
定义全局的测试夹具
"""

import pytest
from fastapi.testclient import TestClient
from sqlmodel import Session, SQLModel, create_engine
from sqlmodel.pool import StaticPool
from app.main import app
from app.dependencies import get_database


@pytest.fixture(name="session")
def session_fixture():
    """创建测试数据库会话"""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    SQLModel.metadata.create_all(engine)
    with Session(engine) as session:
        yield session


@pytest.fixture(name="client")
def client_fixture(session: Session):
    """创建测试客户端"""
    def get_session_override():
        return session

    app.dependency_overrides[get_database] = get_session_override
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear()
