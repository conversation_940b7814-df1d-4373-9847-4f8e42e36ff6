# 应用配置
APP_NAME=路亚百科API
APP_VERSION=0.1.0
DEBUG=false
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USER=root
DATABASE_PASSWORD=cdc123321
DATABASE_NAME=luyabaike

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
EMAIL_RESET_TOKEN_EXPIRE_HOURS=48

# API配置
API_V1_STR=/api/v1
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 文件上传配置
MAX_UPLOAD_SIZE=10485760
UPLOAD_PATH=uploads

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
