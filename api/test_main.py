"""
简化版本的FastAPI应用用于测试
"""

from fastapi import FastAPI
from app.core.config import settings

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="路亚百科API - 为路亚爱好者提供装备和基地信息查询",
)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用{settings.app_name}",
        "version": settings.app_version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "test_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
