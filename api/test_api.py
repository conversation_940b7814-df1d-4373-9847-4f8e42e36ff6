#!/usr/bin/env python3
"""
API接口测试脚本
测试JWT认证系统和CRUD接口
"""

import requests
import json
from typing import Optional

BASE_URL = "http://localhost:8000/api/v1"


class APITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.token: Optional[str] = None
        
    def headers(self):
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers
    
    def test_health_check(self):
        """测试健康检查"""
        print("🔍 Testing health check...")
        try:
            response = requests.get("http://localhost:8000/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    def test_user_registration(self):
        """测试用户注册"""
        print("\n🔍 Testing user registration...")
        data = {
            "username": "testuser",
            "password": "testpass123",
            "screen_name": "Test User",
            "email": "<EMAIL>"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/auth/register",
                headers=self.headers(),
                json=data
            )
            
            if response.status_code == 200:
                print("✅ User registration successful")
                return True
            else:
                print(f"❌ User registration failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ User registration error: {e}")
            return False
    
    def test_user_login(self):
        """测试用户登录"""
        print("\n🔍 Testing user login...")
        data = {
            "username": "testuser",
            "password": "testpass123"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/auth/login",
                headers=self.headers(),
                json=data
            )
            
            if response.status_code == 200:
                result = response.json()
                self.token = result["data"]["access_token"]
                print("✅ User login successful")
                print(f"Token: {self.token[:20]}...")
                return True
            else:
                print(f"❌ User login failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ User login error: {e}")
            return False
    
    def test_get_current_user(self):
        """测试获取当前用户信息"""
        print("\n🔍 Testing get current user...")
        try:
            response = requests.get(
                f"{self.base_url}/auth/me",
                headers=self.headers()
            )
            
            if response.status_code == 200:
                print("✅ Get current user successful")
                return True
            else:
                print(f"❌ Get current user failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Get current user error: {e}")
            return False
    
    def test_fish_crud(self):
        """测试鱼种CRUD操作"""
        print("\n🔍 Testing fish CRUD operations...")
        
        # 创建鱼种
        print("Creating fish...")
        fish_data = {
            "name": "测试鱼种",
            "alias_name": "Test Fish"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/fish",
                headers=self.headers(),
                json=fish_data
            )
            
            if response.status_code == 200:
                fish_id = response.json()["data"]["id"]
                print(f"✅ Fish created with ID: {fish_id}")
                
                # 获取鱼种详情
                print("Getting fish detail...")
                response = requests.get(
                    f"{self.base_url}/fish/{fish_id}",
                    headers=self.headers()
                )
                
                if response.status_code == 200:
                    print("✅ Fish detail retrieved")
                else:
                    print(f"❌ Get fish detail failed: {response.status_code}")
                
                # 更新鱼种
                print("Updating fish...")
                update_data = {"alias_name": "Updated Test Fish"}
                response = requests.put(
                    f"{self.base_url}/fish/{fish_id}",
                    headers=self.headers(),
                    json=update_data
                )
                
                if response.status_code == 200:
                    print("✅ Fish updated")
                else:
                    print(f"❌ Fish update failed: {response.status_code}")
                
                # 删除鱼种
                print("Deleting fish...")
                response = requests.delete(
                    f"{self.base_url}/fish/{fish_id}",
                    headers=self.headers()
                )
                
                if response.status_code == 200:
                    print("✅ Fish deleted")
                    return True
                else:
                    print(f"❌ Fish deletion failed: {response.status_code}")
                    return False
            else:
                print(f"❌ Fish creation failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Fish CRUD error: {e}")
            return False
    
    def test_search_endpoints(self):
        """测试搜索接口"""
        print("\n🔍 Testing search endpoints...")
        
        endpoints = [
            "/fish/search",
            "/rods/search", 
            "/reels/search",
            "/fishing-locations/search"
        ]
        
        success_count = 0
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    print(f"✅ {endpoint} - OK")
                    success_count += 1
                else:
                    print(f"❌ {endpoint} - Failed: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint} - Error: {e}")
        
        return success_count == len(endpoints)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 Starting API tests...\n")
        
        tests = [
            ("Health Check", self.test_health_check),
            ("User Registration", self.test_user_registration),
            ("User Login", self.test_user_login),
            ("Get Current User", self.test_get_current_user),
            ("Fish CRUD", self.test_fish_crud),
            ("Search Endpoints", self.test_search_endpoints),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name} - Unexpected error: {e}")
        
        print(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed!")
        else:
            print("⚠️  Some tests failed. Check the output above for details.")


if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
